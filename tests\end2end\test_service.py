import json
from collections import defaultdict
from pathlib import Path

import pytest
from freezegun import freeze_time
from pytest_mock import Mocker<PERSON><PERSON>ture

from tests.end2end.mocks import mock_things
from tests.end2end.utils import assert_module_output, get_test_files, import_or_reload, init_test_app, load_extra_env

FROZEN_TIME = "2025-01-01 12:00:01"


@pytest.mark.e2e
@pytest.mark.parametrize("file", get_test_files(), ids=lambda f: f.stem)
def test_services(file: Path, mocker: MockerFixture):
    """Test the services.

    A testfile is a json file. It should be a dictionary with the keys "agent_names" and "data".
    The modulename is built using the filename. (e.g. mon_adva.json -> mon_adva, mon_zabbix.ucc.json -> mon_zabbix)
    The "agent_names" key should be a list of agent names. (Usually this is just one, but local6 has multiple)
    The "data" key should be a list of dictionaries. Each dictionary should have the keys "input" and "output".
    The "input" key should be a dictionary with the input data for the test-case.
    The "output" key should be a dictionary with the expected output data for the test-case.

    To add some extra environment variables for the test, you can add a key "extra_env" to the test-file.
    This should be a dictionary with the environment variables you want to add. To ensure this mocked environment is
    taken into account, we always use import_or_reload to reload the module. (Useful for config changes like in zabbix)

    You can force the agent_id to 1337 after a successful run by adding a key "clear_agent_for_next_run" to the
    test-file. This is useful in case of zabbix, which runs multiple times with the same agent_id, but different
    configuration. We force to 1337 to ensure that we still have access to the data, but with a different agent_id.

    You can mock specific things by providing an object under the "mock" key. See mocks.mock_things for more info.

    To skip a specific test, add a key "skip": true to the specific entry in the data list.

    You can also specify a comment for the test-case by adding a key "comment": "comment", this is purely informative.

    For some test-cases, you might want to freeze the time to a specific date.
    (e.g. to ensure the raise_time of a clear is after the raise_time of the problem)
    You can do this by adding a key "frozen_time" to the test-case. If the key is not present, the time will be frozen
    to "2025-01-01 12:00:01". (the FROZEN_TIME constant)
    """
    file_content_json: dict = json.loads(file.read_text())
    load_extra_env(mocker, file_content_json)
    mock_things(mocker, file_content_json)
    modulename = file.name.removesuffix("".join(file.suffixes))
    module = import_or_reload(modulename)
    batches = _batch(file_content_json["data"])
    for frozen_time, inputs in batches.items():
        with freeze_time(frozen_time):
            app = init_test_app(app_name=f"test_app_{file.stem}", input_data=inputs)
            app.run_once()(module.main.__wrapped__)()
    assert_module_output(file_content_json)


def _batch(items: list[dict]):
    """Batch the items by their frozen_time.

    The items are grouped by their frozen_time.
    If no item defines a frozen_time, all items are batched together.

    Returns
    -------
    dict
        A dictionary where the keys are the frozen_time and the values are lists of input items.
    """
    batches = defaultdict(list)
    for item in items:
        if item.get("skip"):
            continue
        frozen_time = item.get("frozen_time", FROZEN_TIME)
        batches[frozen_time].append(item["input"])
    return batches
