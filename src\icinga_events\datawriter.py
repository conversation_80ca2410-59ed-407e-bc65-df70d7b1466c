"""Custom datawriter module for icinga-events."""

import logging
from dataclasses import dataclass, field
from itertools import repeat

from sqlalchemy.orm import Session

from icinga_events.config import config
from icinga_events.icinga_alarm import CustomIcingaAlarm
from icinga_events.utils import add_or_update_host, add_or_update_service, get_process_error
from olympus_common import enums, icinga, utils
from olympus_common.datawriters import DataWriter
from olympus_common.db import create_session, get_alarm_field
from olympus_common.elastic_apm import elastic_apm, trace_scan
from olympus_common.exceptions import OlympusError


@dataclass
class IcingaWriter(DataWriter):
    """Represent a IcingaWriter."""

    host_keys: list[str] = field(default_factory=list)
    service_keys: list[str] = field(default_factory=list)
    should_update_hosts: bool = False
    should_update_services: bool = True
    backend_db_session: Session = create_session(config.service_name)

    @trace_scan(enums.MeasureType.CUSTOM.value)
    def success(self, results: list[dict]) -> None:
        """Add the alarms in Icinga and set a flag to True if everything works fine.

        Add the hosts and events in Icinga if they don't already exist.
        Process a check result on the events.
        Update the process_status field in Alarm if the previous steps succeed.

        Notes
        -----
        The process is split in three loops to avoid Icinga being drowned with too many add requests.
        """
        if not results:
            logging.debug("Nothing to send to the IcingaAPI")
            return None
        icinga_alarms = [CustomIcingaAlarm.from_dict(result) for result in results]
        client = icinga.IcingaClient(use_logging=True)

        # Create arguments for add and update operations
        host_names = []
        encoded_host_names = []
        host_operations = []
        host_details = []
        service_names = []
        encoded_service_names = []
        service_host_names = []
        service_operations = []
        service_alarms = []
        service_details = []
        alarm_summaries = []
        alarm_statuses = []
        host_statuses = []
        host_ci_ids = []
        for idx, alarm in enumerate(icinga_alarms):
            try:
                if alarm.has_missing_enrichment():
                    results[idx]["process_status"] = enums.AlarmJobStatus.NOT_DONE.value
                    continue
                if config.app_env == "prod" and not get_alarm_field(alarm, "actionable"):
                    # In production, only send actionable alarms to Icinga.
                    results[idx]["process_status"] = enums.AlarmJobStatus.NOT_DONE.value
                    continue

                if alarm.hostname not in host_names:
                    if client.get(enums.IcingaObjectType.HOST, alarm.encoded_hostname):
                        if self.should_update_hosts:
                            host_operations.append("update")
                            alarm_details = alarm.details(needed_keys=self.host_keys)
                            # The groups attribute is not allowed for updates.
                            del alarm_details["groups"]
                            host_details.append(alarm_details)
                        else:
                            host_operations.append("none")
                            host_details.append({})
                    else:
                        host_operations.append("add")
                        host_details.append(alarm.details(needed_keys=self.host_keys))
                else:
                    host_operations.append("none")
                    host_details.append({})

                host_names.append(alarm.hostname)
                encoded_host_names.append(alarm.encoded_hostname)
                host_statuses.append(enums.IcingaHostStatus.OK.value)
                host_ci_ids.append(alarm.identification)

                service_alarms.append(alarm.id)
                if alarm.servicename not in service_names:
                    if client.get(enums.IcingaObjectType.SERVICE, alarm.encoded_servicename):
                        if self.should_update_services:
                            service_operations.append("update")
                            alarm_details = alarm.details(needed_keys=self.service_keys)
                            # The groups attribute is not allowed for updates.
                            del alarm_details["groups"]
                            service_details.append(alarm_details)
                        else:
                            service_operations.append("none")
                            service_details.append({})
                    else:
                        service_operations.append("add")
                        service_details.append(alarm.details(needed_keys=self.service_keys))
                else:
                    service_operations.append("none")
                    service_details.append({})

                service_names.append(alarm.servicename)
                encoded_service_names.append(alarm.encoded_servicename)
                service_host_names.append(alarm.hostname)
                alarm_summaries.append(get_alarm_field(alarm, "summary"))
                alarm_statuses.append(alarm.status)
            except Exception as exc:
                results[idx]["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                results[idx]["process_error"] = str(exc)
                elastic_apm.capture_exception()

        client_list_for_hosts = [client] * len(host_names)

        # Loop to add the hosts
        if host_names:
            hosts_args = list(
                zip(
                    client_list_for_hosts,
                    host_names,
                    encoded_host_names,
                    host_details,
                    host_operations,
                    host_ci_ids,
                    host_statuses,
                    strict=False,
                )
            )
            if not (host_exceptions := utils.parallelize_process(hosts_args, add_or_update_host, config.thread_number)):
                for result in results:
                    result["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                    result["process_error"] = "Empty host exceptions"
                raise OlympusError("Unexpected empty host exceptions.")

            if host_exceptions := [exc for exc in host_exceptions if exc["exception"]]:
                host_exceptions = sorted(host_exceptions, key=lambda host_exception: host_exception["id"])

            all_host_exceptions = list(repeat(host_exceptions, len(service_alarms)))

        # Loop to add the services and push their status
        if service_alarms:
            client_list_for_services = [client] * len(service_alarms)

            service_args = list(
                zip(
                    client_list_for_services,
                    service_alarms,
                    service_names,
                    encoded_service_names,
                    service_host_names,
                    service_details,
                    alarm_summaries,
                    alarm_statuses,
                    service_operations,
                    all_host_exceptions,
                    strict=False,
                )
            )

            if not (
                service_exceptions := utils.parallelize_process(
                    service_args, add_or_update_service, config.thread_number
                )
            ):
                for result in results:
                    result["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                    result["process_error"] = "Empty service exceptions"
                raise OlympusError("Unexpected empty service exceptions.")

            service_exceptions = sorted(service_exceptions, key=lambda service_exception: service_exception["id"])

            # Copy the exceptions into the results, to maintain the alarm job status through the datareader.
            for result in results:
                if process_error := get_process_error(result["id"], service_exceptions):
                    result["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                    result["process_error"] = process_error
                elif "process_status" not in result:
                    result["process_status"] = enums.AlarmJobStatus.DONE.value
                elif result["process_status"] != enums.AlarmJobStatus.NOT_DONE.value:
                    result["process_status"] = enums.AlarmJobStatus.DONE.value

        client.show_counter_values()
        client.reset_counter()

        return

    def error(self, _: list[dict], __: Exception) -> None:
        """Do nothing on error."""
