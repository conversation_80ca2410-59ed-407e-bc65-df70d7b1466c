"""Entrypoint for the application."""

from fwd_scom_optic import datawriters, dd
from fwd_scom_optic.config import config
from olympus_common import core, datareaders, db, defaults
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType
from olympus_common.monitoring.monitoring_client import ScriptMonitoringClient
from olympus_common.monitoring.monitoring_manager import MonitoringManager


@trace_scan(MeasureType.CUSTOM.value)
def syslogwriter_kafkareader_app() -> core.Application:
    """Return an Application with a SyslogWriter and KafkaReader."""
    session = db.create_session(config.service_name)
    datareader = datareaders.KafkaReader()
    logger = defaults.get_logger(config.debug, config.logger_config)
    datawriter = datawriters.SyslogWriter()
    monitoring_manager = None
    if not config.disable_monitoring_manager:
        monitoring_client = ScriptMonitoringClient(application_name=config.service_name)
        monitoring_manager = MonitoringManager(
            monitoring_client=monitoring_client, heartbeat_interval_seconds=config.heartbeat_interval_seconds
        )
    return core.Application(
        datareader=datareader,
        datawriter=datawriter,
        logger=logger,
        monitoring_manager=monitoring_manager,
        backend_db_session=session,
    )


application = syslogwriter_kafkareader_app()


@application.run_forever(sleep_time=config.sleep_time)
@trace_scan(MeasureType.CUSTOM.value)
def main(data: list[dict]) -> list[dict]:
    """Run the dd.

    This function is the starting point of the dd and should be used to run the dd.
    """
    return defaults.transform(data, config, dd.run, application.backend_db_session, sort_keys=None, deduplicate=False)
