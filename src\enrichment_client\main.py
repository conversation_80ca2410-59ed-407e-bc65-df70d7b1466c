"""Entrypoint for the application."""

from enrichment_client import enrichment
from enrichment_client.config import config
from olympus_common import defaults
from olympus_common.core import Application
from olympus_common.datareaders import DatabaseReader
from olympus_common.datawriters import DatabaseWriter
from olympus_common.db import Enrichment, create_session
from olympus_common.exceptions import OlympusError
from olympus_common.monitoring.monitoring_client import JobMonitoringClient


def _init_application() -> tuple[Application, JobMonitoringClient]:
    session = create_session(config.service_name)
    datareader = DatabaseReader(session=session, job_name="enrichment")
    logger = defaults.get_logger(config.debug, config.logger_config)
    datawriter = DatabaseWriter(object_class=Enrichment, session=session)
    job_monitoring_client = JobMonitoringClient(application_name=config.service_name)
    application = Application(
        datareader=datareader,
        datawriter=datawriter,
        logger=logger,
        backend_db_session=session,
    )
    return application, job_monitoring_client


application, job_monitoring_client = _init_application()


@application.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    if application.backend_db_session is None:
        raise OlympusError("Missing DB session")

    job_monitoring_client.send_heartbeat()
    enrichments = enrichment.enrichment(alarms_be=data, session=application.backend_db_session)
    job_monitoring_client.check_jobs_in_error(alarm_jobs=data)
    return enrichments
