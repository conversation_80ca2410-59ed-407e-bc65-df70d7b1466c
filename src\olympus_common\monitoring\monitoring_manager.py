"""Monitoring manager with threaded heartbeats.

This contains helpers for the monitoring of olympus projects.
"""

import logging
from typing import <PERSON><PERSON>

from olympus_common.monitoring.heartbeat_thread import HeartbeatThread
from olympus_common.monitoring.monitoring_client import TMonitoringClient

logger = logging.getLogger(__name__)


class MonitoringManager:
    """Manage application monitoring with threaded heartbeats.

    Sending clears and errors is done in the main thread to avoid conflicts with heartbeats. It's the client's
    responsibility to ensure that these methods are called appropriately during the application's lifecycle.
    """

    def __init__(self, monitoring_client: TMonitoringClient, heartbeat_interval_seconds: int = 60):
        """Initialize the monitoring manager.

        Args:
            heartbeat_interval_seconds: Interval between heartbeats in seconds
        """
        self._heartbeat_thread: Optional[HeartbeatThread] = None
        self._heartbeat_interval_seconds = heartbeat_interval_seconds
        self.monitoring_client = monitoring_client

    @property
    def started(self) -> bool:
        """Return if the monitoring manager is started."""
        return self._heartbeat_thread is not None and self._heartbeat_thread.is_alive()

    def start(self):
        """Start the monitoring manager and heartbeat thread."""
        self._heartbeat_thread = HeartbeatThread(
            heartbeat_function=self._send_heartbeat, interval_seconds=self._heartbeat_interval_seconds
        )
        self._heartbeat_thread.start()

        logger.info("Monitoring manager started")

    def stop(self):
        """Stop the monitoring manager and heartbeat thread."""
        if self._heartbeat_thread:
            self._heartbeat_thread.stop()
            self._heartbeat_thread = None
        logger.info("Monitoring manager stopped")

    def _send_heartbeat(self):
        """Send a heartbeat signal to monitoring system."""
        logging.info("Sending heartbeat")
        self.monitoring_client.send_heartbeat()

    def send_success(self, metric_name: str, message: str):
        """Send a success signal to monitoring system.

        This remains in the main thread as it's tied to specific run successes.
        """
        self.monitoring_client.send_heartbeat(metric_name=metric_name, message=message)

    def send_error(self, error_message: str):
        """Send an error signal to monitoring system.

        This remains in the main thread as it's tied to specific run failures.
        """
        # Pause heartbeats during error state to avoid conflicting signals
        if self._heartbeat_thread:
            self._heartbeat_thread.pause()

        logger.info(f"Sending error: {error_message}")
        self.monitoring_client.send_error(error_message)

    def send_clear(self):
        """Clear error state in monitoring system.

        This remains in the main thread as it's tied to specific run successes.
        """
        logger.info("Sending clear")
        self.monitoring_client.send_clear()

        # Resume heartbeats after clearing error
        if self._heartbeat_thread:
            self._heartbeat_thread.resume()
