"""Module that contains functions to provide default behaviour.

Default behaviour is behaviour that (almost) all services use.
"""

import logging
from collections.abc import Callable, Sequence

import numpy as np
import pandas as pd
import uvicorn
from fastapi import FastAPI
from sqlalchemy.orm import Session

from olympus_common import core, datareaders, datawriters, db, profiling
from olympus_common.base_oid_mapper import BaseOIDMapper
from olympus_common.config import BaseServiceConfig, LoggerConfig, ServerConfig, ServiceConfig
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType
from olympus_common.logger import Logger
from olympus_common.monitoring.monitoring_client import ScriptMonitoringClient
from olympus_common.monitoring.monitoring_manager import MonitoringManager
from olympus_common.pd import df_to_excel, ensure_required_fields, tabulate_and_to_excel
from olympus_common.utils import now_timestamp


@trace_scan(transaction_type=MeasureType.CUSTOM.value)
def databasewriter_kafkareader_app(
    config: BaseServiceConfig,
    object_class: type[db.Enrichment | db.Occurrence] = db.Occurrence,
    suffix_service_name: str = "",
) -> core.Application:
    """Return an Application with a DatabaseWriter and KafkaReader.

    If the datawriter should write to another object_class than occurrence, it should be provided.
    """
    application_name = config.service_name
    if suffix_service_name:
        application_name = f"{application_name}_{suffix_service_name}"
    session = db.create_session(application_name=application_name)
    datareader = datareaders.KafkaReader()
    logger = get_logger(config.debug, config.logger_config)
    datawriter = datawriters.DatabaseWriter(object_class=object_class, session=session)

    monitoring_manager = None
    if not config.disable_monitoring_manager:
        monitoring_client = ScriptMonitoringClient(application_name=config.service_name)
        monitoring_manager = MonitoringManager(
            monitoring_client=monitoring_client, heartbeat_interval_seconds=config.heartbeat_interval_seconds
        )

    return core.Application(
        datareader=datareader,
        datawriter=datawriter,
        logger=logger,
        monitoring_manager=monitoring_manager,
        backend_db_session=session,
        profiling_enabled=config.profiling_enabled,
    )


@trace_scan(transaction_type=MeasureType.CUSTOM.value)
def transform(
    data: list[dict],
    config: ServiceConfig,
    run_fn: Callable[[pd.DataFrame, Session | None], pd.DataFrame],
    session: Session | None,
    sort_keys: Sequence[str] | None = ("raise_time", "event_type"),
    sort_ascending: Sequence[bool] | bool = (True, False),
    deduplicate: bool = True,
    oid_mapper: type[BaseOIDMapper] | None = None,
) -> list[dict]:
    """Transform the given data.

    The sort on sort_keys is executed using the transformed column_names after clean_raw_dataframe.
    The argument sort_ascending can be a single boolean or a sequence of booleans, one for each key in sort_keys.
    By default, it sorts by raise_time ascending and event_type descending.
    If deduplicate is True (default), the DataFrame will be deduplicated based on event_type, raise_time, ci_id,
    agent_id, metric_type, metric_name and identifier. This mimics the database's unique key on Occurrence.
    """
    df = pd.json_normalize(data)
    if config.write_outputs:
        df_to_excel(df, config.logger_config.logs_folder, name=f"raw_output_{now_timestamp()}")
    if oid_mapper:
        mapper_instance = oid_mapper()
        transformed_df = mapper_instance.transform_df(df)
    else:
        transformed_df = df
    transformed_df = run_fn(transformed_df, session)
    if config.write_outputs:
        output_name = f"transformed_output_{now_timestamp()}"
        tabulate_and_to_excel(transformed_df, config.logger_config.logs_folder, name=output_name)
    transformed_df = transformed_df.replace({pd.NA: None, np.nan: None})

    if transformed_df.empty:
        return []  # No need to continue if the DataFrame is empty

    if deduplicate:
        deduplicate_keys = ["event_type", "raise_time", "ci_id", "agent_id", "metric_type", "metric_name", "identifier"]
        transformed_df = ensure_required_fields(transformed_df, deduplicate_keys)
        transformed_df = transformed_df.drop_duplicates(subset=deduplicate_keys, keep="first")
    if sort_keys:
        transformed_df = transformed_df.sort_values(by=list(sort_keys), ascending=sort_ascending)
    logging.info(f"Transformed {len(transformed_df)}/{len(df)} records.")
    return transformed_df.to_dict("records")


def run_fastapi_service(app: FastAPI, config: ServerConfig):
    """Set up logging and run the FastAPI service using uvicorn."""
    logger = get_logger(debug=config.debug, logger_config=config.logger_config, setup=True)
    if config.profiling_enabled:
        app.add_middleware(profiling.ProfilingMiddleware, destpath=config.logger_config.logs_folder)
    uvicorn.run(app, host=config.host, port=config.port, log_level=logger.level)


def get_logger(debug: bool, logger_config: LoggerConfig, setup: bool = False) -> Logger:
    """Initialize (and potentially setup) the default logger for an Application.

    When debug is `True`, the debug_level will be used and filename will be None (log to console).
    The folder will be ignored in case we log to console.

    When a filename is set (default case with debug=False), a `TruncatingFileHandler` is used.

    Parameters
    ----------
    debug : bool
        Whether or not to use the debug arguments.
    logger_config: LoggerConfig
        The logger configuration to use.
    setup: bool
        Whether or not to call `setup` on the created logger, by default False

    Returns
    -------
    Logger
        The created `Logger`.
    """
    logger = Logger(
        level=logger_config.default_log_level, folder=logger_config.logs_folder, max_size=logger_config.logs_max_size
    )
    if debug:
        logger.level = logger_config.debug_log_level
        logger.filename = None  # Always log to console in debug mode
    if setup:
        logger.setup()
    return logger
