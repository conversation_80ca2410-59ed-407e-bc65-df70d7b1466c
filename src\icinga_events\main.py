"""Entrypoint for the application."""

import logging

from icinga_events import statics
from icinga_events.config import config
from icinga_events.datawriter import IcingaWriter
from olympus_common import defaults
from olympus_common.core import Application
from olympus_common.datareaders import DatabaseReader
from olympus_common.db import Alarm, create_session
from olympus_common.elastic_apm import elastic_apm
from olympus_common.enums import AlarmJobStatus, ReleaseStatus
from olympus_common.logger import Truncating<PERSON><PERSON><PERSON>and<PERSON>
from olympus_common.monitoring.monitoring_client import JobMonitoringClient
from olympus_common.utils import now_naive


def _init_application() -> tuple[Application, JobMonitoringClient]:
    backend_db_session = create_session(application_name=config.service_name)
    datareader = DatabaseReader(session=backend_db_session, job_name="ui_sending")
    logger = defaults.get_logger(config.debug, config.logger_config)

    if logger.filename:
        for lib_logger in [logging.getLogger("urllib3"), logging.getLogger("urllib3.connectionpool")]:
            handler = TruncatingFileHandler(filename=logger.folder / logger.filename, max_size=logger.max_size)
            lib_logger.addHandler(handler)
            lib_logger.setLevel(logging.WARNING) if not config.debug else lib_logger.setLevel(logging.DEBUG)

    datawriter = IcingaWriter(
        host_keys=statics.HOST_COLUMNS,
        service_keys=statics.SERVICE_COLUMNS,
        should_update_hosts=True,
        should_update_services=True,
        backend_db_session=backend_db_session,
    )

    application = Application(
        datareader=datareader,
        datawriter=datawriter,
        logger=logger,
        backend_db_session=backend_db_session,
    )

    job_monitoring_manager = JobMonitoringClient(application_name=config.service_name)

    return application, job_monitoring_manager


application, job_monitoring_client = _init_application()


@application.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Read alarms from the database, update Icinga with the hosts, services and execute their checks.

    The main logic is in the datawriter, but we first set some alarms as inactive, if they are cleared and are either:
    - without a linked incident
    - with a resolved linked incident

    We manage the monitoring manager ourselves because we want to choose when to send an error even on successful runs.
    """
    # send the heartbeat
    job_monitoring_client.send_heartbeat()

    for alarm in data:
        # If the last occurrence of an alarm is a clear event, the alarm is considered as cleared.
        try:
            if alarm["last_occurrence_id"] == alarm["last_clear_id"]:
                incident = alarm["active_incident"]
                release = alarm["active_release"]
                if (
                    (not incident and not release)
                    or (incident and incident["sap_status"] == "RESO")
                    or (
                        release
                        and release["sap_status"] in [ReleaseStatus.COMPLETED.value, ReleaseStatus.CANCELLED.value]
                    )
                ):
                    alarm["is_active"] = False
                    alarm["closing_time"] = now_naive()
                    alarm_object = Alarm.from_dict(alarm)
                    session = application.backend_db_session or create_session(config.service_name)
                    alarm_object.update_object(session)
        except Exception as exc:
            alarm["process_status"] = AlarmJobStatus.IN_ERROR.value
            alarm["process_error"] = str(exc)
            elastic_apm.capture_exception()

    # Check if there are jobs in error and send an alarm if needed.
    job_monitoring_client.check_jobs_in_error(alarm_jobs=data)

    return data
