"""Monitoring clients.

This contains helpers for the monitoring of olympus projects.
"""

from abc import ABC, abstractmethod
from typing import TypeVar

from sqlalchemy.orm import Session

from olympus_common import enums, utils
from olympus_common.db import Agent, Alarm, Occurrence, create_session
from olympus_common.elastic_apm import trace_scan


class MonitoringClient(ABC):
    """Represent the base MonitoringClient."""

    @abstractmethod
    def send_error(self, message: str) -> None:
        """Send the error to the monitoring tool."""
        pass

    @abstractmethod
    def send_clear(self) -> None:
        """Send the clear to the monitoring tool."""
        pass

    @abstractmethod
    def send_heartbeat(self, metric_name: str | None = None, message: str | None = None) -> None:
        """Send the heartbeat to the monitoring tool."""
        pass


class ScriptMonitoringClient(MonitoringClient):
    """Represent the monitoring client for MON scripts."""

    def __init__(self, application_name: str):
        super().__init__()
        self.application_name = application_name
        self.db_session = create_session(f"{application_name}_heartbeat")

    @trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
    def send_error(self, message: str) -> None:
        """Create an alarm for the error that has arisen in the MON script."""
        occurrence_data = _build_occurrence_for_olympus_agent(
            application_name=self.application_name,
            db_session=self.db_session,
            event_type=enums.AlarmType.PROBLEM.value,
            summary=message,
        )
        Occurrence.insert_one(data=occurrence_data, session=self.db_session)

        self.db_session.commit()

    @trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
    def send_clear(self) -> None:
        """Create a clear for any previous error in the MON script."""
        agent_id = Agent.get_agent_id_from_name(name="Olympus", session=self.db_session)

        if not (
            alarm := Alarm.get_active_alarm_on_metric(
                session=self.db_session,
                agent_id=agent_id,
                ci_id=self.application_name,
                metric_type="/Application/AppHealth/",
                metric_name="mon-error",
            )
        ):
            # No active alarm, so no need to send a clear.
            return
        if alarm.last_clear_id == alarm.last_occurrence_id:
            # A clear has already been sent, so no need to send another one.
            return

        occurrence_data = _build_occurrence_for_olympus_agent(
            application_name=self.application_name,
            db_session=self.db_session,
            event_type=enums.AlarmType.RESOLUTION.value,
            agent_id=agent_id,
        )
        Occurrence.insert_one(data=occurrence_data, session=self.db_session)
        self.db_session.commit()

    @trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
    def send_heartbeat(self, metric_name: str = "mon-heartbeat", message: str | None = None) -> None:
        """Create an alarm for the error that has arisen in the MON script."""
        occurrence_data = _build_occurrence_for_olympus_agent(
            application_name=self.application_name,
            db_session=self.db_session,
            event_type=enums.AlarmType.HEARTBEAT.value,
            metric_name=metric_name,
            summary=message or f"{self.application_name} Heartbeat Message",
        )
        Occurrence.insert_one(data=occurrence_data, session=self.db_session)
        self.db_session.commit()


TMonitoringClient = TypeVar("TMonitoringClient", bound=MonitoringClient)


def _build_occurrence_for_olympus_agent(
    application_name: str,
    db_session: Session,
    event_type: str,
    metric_name: str | None = None,
    agent_id: int | None = None,
    summary: str | None = None,
) -> dict:
    """Build an Occurrence dictionary for the monitoring of Olympus."""
    if agent_id is None:
        agent_id = Agent.get_agent_id_from_name(name="Olympus", session=db_session)

    if event_type == enums.AlarmType.PROBLEM.value:
        metric_name = "mon-error"
        severity = enums.Severity.CRITICAL.value
    elif event_type == enums.AlarmType.RESOLUTION.value:
        metric_name = "mon-error"
        severity = enums.Severity.CLEARED.value
    elif event_type == enums.AlarmType.HEARTBEAT.value:
        if not metric_name:
            metric_name = "mon-heartbeat"
        severity = enums.Severity.CLEARED.value

    return {
        "action_class": enums.Scope.IT.value,
        "agent_id": agent_id,
        "ci_id": application_name,
        "clear_type": enums.ClearType.AUTOMATICALLY.value,
        "event_type": event_type,
        "handle_time": utils.now_naive(),
        "manager": application_name.replace("_", "-"),
        "metric_type": "/Application/AppHealth/",
        "metric_name": metric_name,
        "raise_time": utils.now_naive(),
        "severity": severity,
        "summary": summary,
        "top_level": "A2110",
        "wake_up_time": utils.now_naive(),
    }


class JobMonitoringClient(ScriptMonitoringClient):
    """Represent the monitoring client for jobs other than the mon-scripts.

    This class is mainly used for the enrichment job and the icinga-events job.
    """

    @trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
    def check_jobs_in_error(self, alarm_jobs: list[dict]) -> None:
        """Check if there are jobs in error and send an alarm if needed."""
        has_error = False
        errors = []
        for job in alarm_jobs:
            if job.get("process_status") != enums.AlarmJobStatus.IN_ERROR.value:
                continue
            errors.append(job["process_error"])
            has_error = True

        if has_error:
            self.send_errors(messages=errors)
        else:
            self.send_clear()

    @trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
    def send_errors(self, messages: list[str]) -> None:
        """Send multiple errors to the monitoring tool."""
        occurrence_data = [
            _build_occurrence_for_olympus_agent(
                application_name=self.application_name,
                db_session=self.db_session,
                event_type=enums.AlarmType.PROBLEM.value,
                summary=message,
            )
            for message in messages
        ]
        Occurrence.insert_many(data=occurrence_data, session=self.db_session)

        self.db_session.commit()
