{"data": [{"input": {"key": null, "source": {"ip": "*********"}, "event": {"uuid": "f08992bc-7ab8-47d3-ac60-f8992f17a95a", "code": "0x00210e12", "kafka": {"topic": "a1290-spectrum-events-prd", "offset": 2430052, "partition": 2, "timestamp": "2025-07-22T08:17:14.467Z", "key": null, "consumer_group": "a1559-logstash-a1290-spectrum-events-prd"}, "created": "2025-07-22T08:17:14.602324Z", "from_trap": true, "code_type": "A 'ciscoLwappApIfUpNotify' event has occurred, from {t} device, named {m}.\n\nThis notification is generated when AP's Interface operational status goes up.\n\ncLApSysMacAddress = {S 1}\ncLApDot11IfSlotId = {U 2}\ncLApDot11IfSlotId.cLApSysMacAddress.cLApDot11IfSlotId = {o 3}\ncLApPortNumber = {U 4}\ncLApIfUpDownFailureType = {T cLApIfUpDownFailureType 5}\ncLApIfUpDownCause = {S 6}\ncLApIfUpDownFailureCode = {S 7}\ncLApName = {S 8}", "logstash": {"instance_name": "iictniapls016"}}, "@timestamp": "2025-07-22T08:17:12Z", "message": "Tue 22 Jul, 2025 - 10:17:12 - A \"ciscoLwappApIfUpNotify\" event has occurred, from SwCiscoIOS device, named iictpiwlc01.network.railb.be.\n\nThis notification is generated when AP's Interface operational status goes up.\n\ncLApSysMacAddress = 88.9C.AD.68.A5.A0\ncLApDot11IfSlotId = 0\ncLApDot11IfSlotId.cLApSysMacAddress.cLApDot11IfSlotId = *************.*************\ncLApPortNumber = 2\ncLApIfUpDownFailureType = detectedFailure\ncLApIfUpDownCause = Unknown\ncLApIfUpDownFailureCode = \ncLApName = lpmonsx40\n\n(event [0x00210e12])\nOnly displaying most recent of 2 event messages.\n", "@version": "1", "network": {"type": "ipv4"}, "alarm": {"repair_person": "", "ackd": "FALSE", "landscape": "0x96000000", "model_type": "SwCiscoIOS", "status": "", "id": "43155054", "cause_code_message": "\n\nSYMPTOMS:\n\nAccess Point's Interface operational status down.\n\nPROBABLE CAUSES:\n\nAccess Point's Interface down.\n\nRECOMMENDED ACTIONS:\n\n1) Refer to the Event Message associated with this alarm for additional details that the device may have provided about the cause of this condition.\n\n2) Review the Events associated with this model that occurred in the same time frame as this alarm in order to gain insight into the device's state.  These can be viewed from the Events tab in OneClick.", "type": "Set", "model_handle": "0x96003055", "clearable": "TRUE", "cause_code": "0x00210e13", "model_name": "iictpiwlc01.network.railb.be", "state": "NEW", "model_type_handle": "0x2100b2", "security_string": "", "severity": "MINOR", "cause_code_type": "AP INTERFACE DOWN"}, "device": {"type": "Catalyst 9800-80 WLC"}, "observer": {"hostname": "iictyiaplv295"}, "Ndm": null}, "output": {}, "comment": "Not in ca_spectrum list, so should not be monitored/processed."}, {"input": {"key": null, "source": {"ip": "*************"}, "event": {"uuid": "716787fe-0059-4414-a027-1ef01c4b8acf", "code": "0xfff002ea", "kafka": {"topic": "a1290-spectrum-events-prd", "offset": 2423137, "partition": 2, "timestamp": "2025-07-20T21:36:17.146Z", "key": null, "consumer_group": "a1559-logstash-a1290-spectrum-events-prd"}, "created": "2025-07-20T21:36:17.2850015Z", "from_trap": false, "code_type": "<PERSON><PERSON> received from device {m} of type {t}. Device Time {G 0x2302d0}. (Trap type {S 1})\nTrap var bind data: {S 2}\n\nPoolName={S 111}", "logstash": {"instance_name": "iictniapls016"}}, "@timestamp": "2025-07-20T21:36:09Z", "message": "Sun 20 Jul, 2025 - 23:36:09 - <PERSON><PERSON> received from device f5mechl2-prod.msnet.railb.be of type F5BigIPDev. Device Time 10+13:56:52. (Trap type *******.4.1.3375.2.4.6.302)\nTrap var bind data: \nOID:  *******.*******.0  Value:  ********\nOID:  *******.*******.4.1.0  Value:  *******.4.1.3375.2.4.0.302\nOID:  *******.4.1.3375.*******  Value:  Pool /hrrail/a1977-ma-accountappapi_https_pool now has available members (slot1)\nOID:  *******.*******.4.3.0  Value:  *******.4.1.3375.2.4\n\nPoolName=/hrrail/a1977-ma-accountappapi_https_pool\n\n(event [0xfff002ea])\nOnly displaying most recent of 4 event messages.\n", "@version": "1", "network": {"type": "ipv4"}, "alarm": {"repair_person": "", "ackd": "FALSE", "landscape": "0x96000000", "model_type": "F5BigIPDev", "status": "", "id": "********", "cause_code_message": "No members available for pool\n\nSYMPTOMS:\n\n\n\nPROBABLE CAUSES:\n\n\n\nRECOMMENDED ACTIONS:\n\n1) Refer to the Event Message associated with this alarm for additional details that the device may have provided about the cause of this condition.\n\n2) Review the Events associated with this model that occurred in the same time frame as this alarm in order to gain insight into the device's state.  These can be viewed from the Events tab in OneClick.", "type": "Set", "model_handle": "0x96056a47", "clearable": "TRUE", "cause_code": "0xfff002e9", "model_name": "f5mechl2-prod.msnet.railb.be", "state": "NEW", "model_type_handle": "0x3b7000e", "security_string": "", "severity": "MAJOR", "cause_code_type": "No members available for pool"}, "device": {"type": "BigIP vCMP"}, "observer": {"hostname": "iictyiaplv295"}, "Ndm": {"OperatingSystem": "", "Role": "Load Balancer F5", "Description": "BIG-IP vCMP Guest", "Name": "f5mechl2-prod", "NetworkType": "", "ModelName": "", "Status": 1, "ManagementIp": "", "SapCode": "IP000328", "Owner": null, "FunctionalLocation": "", "DenominationFr": "MUIZE.DC.0001.MUIP11", "DenominationNl": "MUIZE.DC.0001.MUIP11", "Criticality": 2}}, "output": {"metric_type": "/HardwareEvent/", "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "event_type": "problem", "ci_id": "hrrail/a1977-ma-accountappapi_https_pool:f5mechl2-prod", "node": "*************", "node_alias": "*************", "metric_name": "fff002e9", "summary": "No members available for pool_hrrail/a1977-ma-accountappapi_https_pool", "event_id": "********", "severity": 5, "raise_time": "2025-07-20 21:36:09", "wake_up_time": "2025-07-20 21:38:09", "clear_time": null}}, {"input": {"key": null, "source": {"ip": "*************"}, "event": {"uuid": "b1b29af7-683e-4fd7-a8d4-2e221bb4e735", "code": "0xfff002ea", "kafka": {"topic": "a1290-spectrum-events-prd", "offset": 2425717, "partition": 0, "timestamp": "2025-07-20T21:36:17.576Z", "key": null, "consumer_group": "a1559-logstash-a1290-spectrum-events-prd"}, "created": "2025-07-20T21:36:17.6117794Z", "from_trap": false, "code_type": "<PERSON><PERSON> received from device {m} of type {t}. Device Time {G 0x2302d0}. (Trap type {S 1})\nTrap var bind data: {S 2}\n\nPoolName={S 111}", "logstash": {"instance_name": "iictmiapls016"}}, "@timestamp": "2025-07-20T21:36:09Z", "message": "Sun 20 Jul, 2025 - 23:36:09 - <PERSON><PERSON> received from device f5mechl2-prod.msnet.railb.be of type F5BigIPDev. Device Time 10+13:56:52. (Trap type *******.4.1.3375.2.4.6.302)\nTrap var bind data: \nOID:  *******.*******.0  Value:  ********\nOID:  *******.*******.4.1.0  Value:  *******.4.1.3375.2.4.0.302\nOID:  *******.4.1.3375.*******  Value:  Pool /hrrail/a1977-ma-accountappapi_https_pool now has available members (slot1)\nOID:  *******.*******.4.3.0  Value:  *******.4.1.3375.2.4\n\nPoolName=/hrrail/a1977-ma-accountappapi_https_pool\n\n(event [0xfff002ea])\nOnly displaying most recent of 4 event messages.\n", "@version": "1", "network": {"type": "ipv4"}, "alarm": {"repair_person": "", "ackd": "FALSE", "landscape": "0x96000000", "model_type": "F5BigIPDev", "status": "", "id": "********", "cause_code_message": "No members available for pool\n\nSYMPTOMS:\n\n\n\nPROBABLE CAUSES:\n\n\n\nRECOMMENDED ACTIONS:\n\n1) Refer to the Event Message associated with this alarm for additional details that the device may have provided about the cause of this condition.\n\n2) Review the Events associated with this model that occurred in the same time frame as this alarm in order to gain insight into the device's state.  These can be viewed from the Events tab in OneClick.", "type": "Update", "model_handle": "0x96056a47", "clearable": "TRUE", "cause_code": "0xfff002e9", "model_name": "f5mechl2-prod.msnet.railb.be", "state": "NEW", "model_type_handle": "0x3b7000e", "security_string": "", "severity": "MAJOR", "cause_code_type": "No members available for pool"}, "device": {"type": "BigIP vCMP"}, "observer": {"hostname": "iictyiaplv295"}, "Ndm": {"OperatingSystem": "", "Role": "Load Balancer F5", "Description": "BIG-IP vCMP Guest", "Name": "f5mechl2-prod", "NetworkType": "", "ModelName": "", "Status": 1, "ManagementIp": "", "SapCode": "IP000328", "Owner": null, "FunctionalLocation": "", "DenominationFr": "MUIZE.DC.0001.MUIP11", "DenominationNl": "MUIZE.DC.0001.MUIP11", "Criticality": 2}}, "output": {"metric_type": "/HardwareEvent/", "manager": "mon-ca-spectrum", "action_class": "IT", "clear_type": "automatic", "event_type": "problem", "ci_id": "hrrail/a1977-ma-accountappapi_https_pool:f5mechl2-prod", "node": "*************", "node_alias": "*************", "metric_name": "fff002e9", "summary": "No members available for pool_hrrail/a1977-ma-accountappapi_https_pool", "event_id": "********", "severity": 5, "raise_time": "2025-07-20 21:36:09", "wake_up_time": "2025-07-20 21:38:09", "clear_time": null}, "ignore_in_batch": "Ignored in batch because ci_id, metric_name, metric_type and raise_time are the other message with id ********."}, {"input": {"key": null, "source": {"ip": "*************"}, "event": {"uuid": "e818657f-bfe6-4472-a3e4-24036ca01916", "code": "0xfff002ea", "kafka": {"topic": "a1290-spectrum-events-prd", "offset": 2425723, "partition": 0, "timestamp": "2025-07-20T21:36:21.45Z", "key": null, "consumer_group": "a1559-logstash-a1290-spectrum-events-prd"}, "created": "2025-07-20T21:36:21.4867187Z", "from_trap": false, "code_type": "<PERSON><PERSON> received from device {m} of type {t}. Device Time {G 0x2302d0}. (Trap type {S 1})\nTrap var bind data: {S 2}\n\nPoolName={S 111}", "logstash": {"instance_name": "iictmiapls016"}}, "@timestamp": "2025-07-20T21:36:09Z", "message": "Sun 20 Jul, 2025 - 23:36:09 - <PERSON><PERSON> received from device f5mechl2-prod.msnet.railb.be of type F5BigIPDev. Device Time 10+13:56:52. (Trap type *******.4.1.3375.2.4.6.302)\nTrap var bind data: \nOID:  *******.*******.0  Value:  ********\nOID:  *******.*******.4.1.0  Value:  *******.4.1.3375.2.4.0.302\nOID:  *******.4.1.3375.*******  Value:  Pool /hrrail/a1977-ma-accountsbackend_https_pool now has available members (slot1)\nOID:  *******.*******.4.3.0  Value:  *******.4.1.3375.2.4\n\nPoolName=/hrrail/a1977-ma-accountsbackend_https_pool\n\n(event [0xfff002ea])\nOnly displaying most recent of 4 event messages.\n", "@version": "1", "network": {"type": "ipv4"}, "alarm": {"repair_person": "", "ackd": "FALSE", "landscape": "0x96000000", "model_type": "F5BigIPDev", "status": "", "id": "********", "cause_code_message": "No members available for pool\n\nSYMPTOMS:\n\n\n\nPROBABLE CAUSES:\n\n\n\nRECOMMENDED ACTIONS:\n\n1) Refer to the Event Message associated with this alarm for additional details that the device may have provided about the cause of this condition.\n\n2) Review the Events associated with this model that occurred in the same time frame as this alarm in order to gain insight into the device's state.  These can be viewed from the Events tab in OneClick.", "type": "Set", "model_handle": "0x96056a47", "clearable": "TRUE", "cause_code": "0xfff002e9", "model_name": "f5mechl2-prod.msnet.railb.be", "state": "NEW", "model_type_handle": "0x3b7000e", "security_string": "", "severity": "MAJOR", "cause_code_type": "No members available for pool"}, "device": {"type": "BigIP vCMP"}, "observer": {"hostname": "iictyiaplv295"}, "Ndm": {"OperatingSystem": "", "Role": "Load Balancer F5", "Description": "BIG-IP vCMP Guest", "Name": "f5mechl2-prod", "NetworkType": "", "ModelName": "", "Status": 1, "ManagementIp": "", "SapCode": "IP000328", "Owner": null, "FunctionalLocation": "", "DenominationFr": "MUIZE.DC.0001.MUIP11", "DenominationNl": "MUIZE.DC.0001.MUIP11", "Criticality": 2}}, "output": {"event_type": "problem", "event_id": "********", "ci_id": "hrrail/a1977-ma-accountsbackend_https_pool:f5mechl2-prod", "node": "*************", "metric_name": "fff002e9", "severity": 5}}, {"input": {"key": null, "source": {"ip": "*************"}, "event": {"uuid": "d305f904-683e-4366-a5ca-cee225387600", "code": "0xfff002ea", "kafka": {"topic": "a1290-spectrum-events-prd", "offset": 2426540, "partition": 1, "timestamp": "2025-07-20T21:36:21.785Z", "key": null, "consumer_group": "a1559-logstash-a1290-spectrum-events-prd"}, "created": "2025-07-20T21:36:21.921014Z", "from_trap": false, "code_type": "<PERSON><PERSON> received from device {m} of type {t}. Device Time {G 0x2302d0}. (Trap type {S 1})\nTrap var bind data: {S 2}\n\nPoolName={S 111}", "logstash": {"instance_name": "iictniapls015"}}, "@timestamp": "2025-07-20T21:36:09Z", "message": "Sun 20 Jul, 2025 - 23:36:09 - <PERSON><PERSON> received from device f5mechl2-prod.msnet.railb.be of type F5BigIPDev. Device Time 10+13:56:52. (Trap type *******.4.1.3375.2.4.6.302)\nTrap var bind data: \nOID:  *******.*******.0  Value:  ********\nOID:  *******.*******.4.1.0  Value:  *******.4.1.3375.2.4.0.302\nOID:  *******.4.1.3375.*******  Value:  Pool /hrrail/a1977-ma-accountsbackend_https_pool now has available members (slot1)\nOID:  *******.*******.4.3.0  Value:  *******.4.1.3375.2.4\n\nPoolName=/hrrail/a1977-ma-accountsbackend_https_pool\n\n(event [0xfff002ea])\nOnly displaying most recent of 4 event messages.\n", "@version": "1", "network": {"type": "ipv4"}, "alarm": {"repair_person": "", "ackd": "FALSE", "landscape": "0x96000000", "model_type": "F5BigIPDev", "status": "", "id": "********", "cause_code_message": "No members available for pool\n\nSYMPTOMS:\n\n\n\nPROBABLE CAUSES:\n\n\n\nRECOMMENDED ACTIONS:\n\n1) Refer to the Event Message associated with this alarm for additional details that the device may have provided about the cause of this condition.\n\n2) Review the Events associated with this model that occurred in the same time frame as this alarm in order to gain insight into the device's state.  These can be viewed from the Events tab in OneClick.", "type": "Update", "model_handle": "0x96056a47", "clearable": "TRUE", "cause_code": "0xfff002e9", "model_name": "f5mechl2-prod.msnet.railb.be", "state": "NEW", "model_type_handle": "0x3b7000e", "security_string": "", "severity": "MAJOR", "cause_code_type": "No members available for pool"}, "device": {"type": "BigIP vCMP"}, "observer": {"hostname": "iictyiaplv295"}, "Ndm": {"OperatingSystem": "", "Role": "Load Balancer F5", "Description": "BIG-IP vCMP Guest", "Name": "f5mechl2-prod", "NetworkType": "", "ModelName": "", "Status": 1, "ManagementIp": "", "SapCode": "IP000328", "Owner": null, "FunctionalLocation": "", "DenominationFr": "MUIZE.DC.0001.MUIP11", "DenominationNl": "MUIZE.DC.0001.MUIP11", "Criticality": 2}}, "output": {"event_type": "problem", "event_id": "********", "ci_id": "hrrail/a1977-ma-accountsbackend_https_pool:f5mechl2-prod", "node": "*************", "metric_name": "fff002e9", "severity": 5}, "ignore_in_batch": "Ignored in batch because ci_id, metric_name, metric_type and raise_time are the other message with id ********."}, {"input": {"key": null, "source": {"ip": "*************"}, "event": {"uuid": "ea14aa67-cc50-457d-b705-2a21d5ddd132", "code": "0xfff002ea", "kafka": {"topic": "a1290-spectrum-events-prd", "offset": 2426560, "partition": 1, "timestamp": "2025-07-20T21:36:53.24Z", "key": null, "consumer_group": "a1559-logstash-a1290-spectrum-events-prd"}, "created": "2025-07-20T21:36:53.3735096Z", "from_trap": false, "code_type": "<PERSON><PERSON> received from device {m} of type {t}. Device Time {G 0x2302d0}. (Trap type {S 1})\nTrap var bind data: {S 2}\n\nPoolName={S 111}", "logstash": {"instance_name": "iictniapls015"}}, "@timestamp": "2025-07-20T21:36:09Z", "message": "Sun 20 Jul, 2025 - 23:36:09 - <PERSON><PERSON> received from device f5mechl2-prod.msnet.railb.be of type F5BigIPDev. Device Time 10+13:56:52. (Trap type *******.4.1.3375.2.4.6.302)\nTrap var bind data: \nOID:  *******.*******.0  Value:  ********\nOID:  *******.*******.4.1.0  Value:  *******.4.1.3375.2.4.0.302\nOID:  *******.4.1.3375.*******  Value:  Pool /hrrail/a1977-ma-accountappapi_https_pool now has available members (slot1)\nOID:  *******.*******.4.3.0  Value:  *******.4.1.3375.2.4\n\nPoolName=/hrrail/a1977-ma-accountappapi_https_pool\n\n(event [0xfff002ea])\nOnly displaying most recent of 4 event messages.\n", "@version": "1", "network": {"type": "ipv4"}, "alarm": {"repair_person": "", "ackd": "FALSE", "landscape": "0x96000000", "model_type": "F5BigIPDev", "status": "", "id": "********", "cause_code_message": "No members available for pool\n\nSYMPTOMS:\n\n\n\nPROBABLE CAUSES:\n\n\n\nRECOMMENDED ACTIONS:\n\n1) Refer to the Event Message associated with this alarm for additional details that the device may have provided about the cause of this condition.\n\n2) Review the Events associated with this model that occurred in the same time frame as this alarm in order to gain insight into the device's state.  These can be viewed from the Events tab in OneClick.", "type": "Clear", "model_handle": "0x96056a47", "clearable": "TRUE", "cause_code": "0xfff002e9", "model_name": "f5mechl2-prod.msnet.railb.be", "state": "NEW", "model_type_handle": "0x3b7000e", "security_string": "", "severity": "MAJOR", "cause_code_type": "No members available for pool"}, "device": {"type": "BigIP vCMP"}, "observer": {"hostname": "iictyiaplv295"}, "Ndm": {"OperatingSystem": "", "Role": "Load Balancer F5", "Description": "BIG-IP vCMP Guest", "Name": "f5mechl2-prod", "NetworkType": "", "ModelName": "", "Status": 1, "ManagementIp": "", "SapCode": "IP000328", "Owner": null, "FunctionalLocation": "", "DenominationFr": "MUIZE.DC.0001.MUIP11", "DenominationNl": "MUIZE.DC.0001.MUIP11", "Criticality": 2}}, "output": {"event_type": "clear", "ci_id": "hrrail/a1977-ma-accountappapi_https_pool:f5mechl2-prod", "node": "*************", "metric_name": "fff002e9", "event_id": "********", "severity": 5, "raise_time": "2025-07-20 21:36:09", "wake_up_time": "2025-07-20 21:38:09", "clear_time": "2025-07-20 21:36:09"}}]}