"""Entrypoint for heartbeat_jobs."""

import logging
from datetime import datetime

from sqlalchemy import and_, or_, select, text

from heartbeat_job.config import config
from heartbeat_job.models import HeartbeatD<PERSON>, THasKeyAttributes
from heartbeat_job.statics import GET_MISSING_HEARTBEATS_QUERY
from olympus_common import db, defaults, enums, utils
from olympus_common.core import Application
from olympus_common.datareaders import DatabaseReader
from olympus_common.datawriters import DatabaseWriter
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType


@trace_scan(MeasureType.CUSTOM.value)
def _init_application():
    session = db.create_session(config.service_name)
    datareader = DatabaseReader(session, "agent_heartbeat")
    datawriter = DatabaseWriter(session, db.Occurrence)
    logger = defaults.get_logger(config.debug, config.logger_config)
    return Application(datareader=datareader, datawriter=datawriter, logger=logger, backend_db_session=session)


application = _init_application()


@application.run_forever(sleep_time=config.sleep_time)
@trace_scan(MeasureType.CUSTOM.value)
def main(data: list[dict]) -> list[dict]:
    """Process all heartbeats efficiently."""
    if not data:
        return []

    agent_heartbeats = [db.AgentHeartbeat.from_dict(heartbeat) for heartbeat in data]
    active_alarms: dict[tuple, db.Alarm] = _get_active_heartbeat_alarms(agent_heartbeats)

    missing_heartbeats = _get_missing_heartbeats()
    missing_heartbeat_keys = {_create_key(heartbeat) for heartbeat in missing_heartbeats}

    missing_heartbeat_data = _create_missing_heartbeats(missing_heartbeats, active_alarms)
    clear_data = _create_clears(active_alarms, missing_heartbeat_keys)

    results = missing_heartbeat_data + clear_data
    return results


def _get_missing_heartbeats() -> list[HeartbeatData]:
    """Check all heartbeats in a single query."""
    missing_heartbeats = application.backend_db_session.execute(text(GET_MISSING_HEARTBEATS_QUERY)).fetchall()
    missing_heartbeat_data = [HeartbeatData(*row) for row in missing_heartbeats]
    return missing_heartbeat_data


def _create_clears(active_alarms: dict[tuple, db.Alarm], missing_heartbeat_keys: set[tuple]):
    clear_alarms = []
    for alarm_key, active_alarm in active_alarms.items():
        if alarm_key in missing_heartbeat_keys:
            continue  # There is a missing heartbeat for this alarm_key, do not clear.
        if active_alarm.last_clear_id == active_alarm.last_occurrence_id:
            continue  # Do not create duplicate clears
        agent = active_alarm.agent
        if not agent:
            logging.warning(
                f"Alarm {active_alarm.id} has no agent, skipping clear for {active_alarm.ci_id} - "
                f"{active_alarm.metric_name}"
            )
            continue
        alarm_clear = from_alarm_data(
            agent_name=agent.name,
            ci_id=active_alarm.ci_id,
            agent_id=active_alarm.agent_id,
            metric_type=active_alarm.metric_type,
            metric_name=active_alarm.metric_name,
            agent_action_class=agent.action_class,
            event_type=enums.AlarmType.RESOLUTION.value,
        )
        clear_alarms.append(alarm_clear)
    return clear_alarms


def _create_missing_heartbeats(missing_heartbeats: list[HeartbeatData], active_alarms: dict[tuple, db.Alarm]):
    missing_heartbeat_data = []
    for missing_heartbeat in missing_heartbeats:
        key = _create_key(missing_heartbeat)
        if key in active_alarms:
            continue  # Do not create duplicate problems
        missing_alarm = from_alarm_data(
            agent_name=missing_heartbeat.agent_name,
            ci_id=missing_heartbeat.ci_id,
            agent_id=missing_heartbeat.agent_id,
            metric_type=missing_heartbeat.metric_type,
            metric_name=missing_heartbeat.metric_name,
            agent_action_class=missing_heartbeat.action_class,
            event_type=enums.AlarmType.PROBLEM.value,
            last_heartbeat_time=missing_heartbeat.last_time,
        )
        missing_heartbeat_data.append(missing_alarm)
    return missing_heartbeat_data


def _create_key(data: THasKeyAttributes):
    """Create a key for the data."""
    return (data.agent_id, data.ci_id, data.metric_name, data.metric_type)


def _get_active_heartbeat_alarms(agent_heartbeats: list[db.AgentHeartbeat]) -> dict:
    """Get all active alarms for the missing heartbeats."""
    conditions = []
    for agent_heartbeat in agent_heartbeats:
        if not agent_heartbeat.is_active:
            continue
        clause = and_(
            db.Alarm.ci_id == agent_heartbeat.ci_id,
            db.Alarm.agent_id == agent_heartbeat.agent_id,
            db.Alarm.metric_name == agent_heartbeat.metric_name,
            db.Alarm.metric_type == agent_heartbeat.metric_type,
        )
        conditions.append(clause)
    stmt = select(db.Alarm).where(and_(db.Alarm.is_active.is_(True), or_(*conditions)))
    active_alarms = application.backend_db_session.scalars(stmt).all()
    return {_create_key(alarm): alarm for alarm in active_alarms}


def from_alarm_data(
    agent_name: str | None,
    ci_id: str | None,
    agent_id: int | None,
    metric_type: str | None,
    metric_name: str | None,
    agent_action_class: str | None,
    event_type: str | None,
    last_heartbeat_time: datetime | None = None,
) -> dict:
    """Construct a dictionary that can be passed to datawriters."""
    prefix = f"{agent_name} - {ci_id}"
    if event_type == enums.AlarmType.PROBLEM.value:
        if not last_heartbeat_time:
            raise ValueError("A last_heartbeat_time is expected to generate an alarm.")

        summary = f"{prefix} : No heartbeat since {datetime.strftime(last_heartbeat_time, '%d-%m-%Y %H:%M:%S')}"
    else:
        summary = f"{prefix} : Heartbeat received"

    current_time = utils.now_naive()
    return {
        "summary": summary,
        "severity": (
            enums.Severity.CLEARED.value
            if event_type == enums.AlarmType.RESOLUTION.value
            else enums.Severity.CRITICAL.value
        ),
        "event_type": event_type,
        "raise_time": current_time,
        "wake_up_time": current_time,
        "handle_time": current_time,
        "clear_time": current_time if event_type == enums.AlarmType.RESOLUTION.value else None,
        "ci_id": ci_id,
        "agent_id": agent_id,
        "metric_type": metric_type,
        "metric_name": metric_name,
        "actionable": True,
        "action_class": agent_action_class,
        "manager": "heartbeat-job",
        "clear_type": enums.ClearType.AUTOMATICALLY.value,
    }
